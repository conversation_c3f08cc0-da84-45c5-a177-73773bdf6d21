<template>
  <div class="app-container">
    <el-form
      ref="queryForm"
      :model="queryParams"
      :inline="true"
    >
      <el-form-item>
        <el-select
          v-model="queryParams.type"
          placeholder="请选择"
          style="width: 100px;"
        >
          <el-option
            label="手机号"
            value="mobile"
          />
          <el-option
            label="订单编号"
            value="orderId"
          />
          <el-option
            label="身份证号"
            value="certNo"
          />
          <el-option
            label="外部编号"
            value="outerOrderId"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="queryParams.typeText"
          @keyup.enter.native="handleQuery"
          placeholder="请输入"
          clearable
          style="width:290px;"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          round
          type="primary"
          icon="el-icon-search"
          size="mini"
          :disabled="activeName !== 'first'"
          @click="handleQuery"
        >
          查询
        </el-button>
        <el-button
          v-permission="['admin', 'order:info:call']"
          round
          :disabled="!currentRow"
          type="success"
          icon="el-icon-phone-outline"
          size="mini"
          @click="handlePhoneCall"
        >
          拨打电话
        </el-button>
      </el-form-item>
    </el-form>

    <el-tabs
      v-model="activeName"
      type="card"
      @tab-click="handleClick"
    >
      <el-tab-pane
        label="订单详情"
        name="first"
      >
        <el-table
          v-loading="loading"
          border="border"
          :data="list"
        >
          <el-table-column
            label="操作"
            align="center"
            fixed
            width="200"
          >
            <template slot-scope="scope">
              <el-button
                round
                type="primary"
                plain
                size="mini"
                @click="choose(scope.row)"
              >
                更多
              </el-button>
              <el-button
                round
                size="mini"
                type="primary"
                @click="handleCustomerInfo(scope.row.userId, scope.row.orderId)"
              >
                客户信息
              </el-button>
            </template>
          </el-table-column>
          <el-table-column
            label="订单编号"
            prop="orderId"
            align="center"
            min-width="180"
          />
          <el-table-column
            label="外部编号"
            prop="outerOrderId"
            align="center"
            min-width="180"
          />
          <el-table-column
            label="姓名"
            prop="name"
            align="center"
          />
          <el-table-column
            label="期数"
            prop="applyPeriods"
            align="center"
            width="50"
          />
          <el-table-column
            label="利率"
            prop="irrRate"
            align="center"
          />
          <el-table-column
            label="审批金额"
            prop="approveAmount"
            align="center"
          />
          <el-table-column
            label="申请金额"
            prop="applyAmount"
            align="center"
          />
          <el-table-column
            label="放款金额"
            prop="loanAmount"
            align="center"
          />
          <el-table-column
            label="会员供应商"
            prop="rightsSupplier"
            align="center"
            width="100"
          />
          <el-table-column
            label="扣款权益通道"
            prop="rightsPayChannel"
            align="center"
            width="120"
            :formatter="rightsPayChannelFormat"
          />
          <el-table-column
            label="权益金额"
            prop="rightsAmount"
            align="center"
          />
          <el-table-column
            label="实际金额"
            prop="rightsActualAmount"
            align="center"
          />
          <el-table-column
            label="权益状态"
            prop="rightsStatus"
            align="center"
          />
          <el-table-column
            label="融担公司"
            prop="guaranteeCompany"
            align="center"
          />
          <el-table-column
            label="资金方"
            prop="bankChannel"
            align="center"
            :formatter="bankChannelFormat"
          />
          <el-table-column
            label="进件渠道"
            prop="flowChannel"
            align="center"
            :formatter="flowChannelFormat"
          />
          <el-table-column
            label="订单状态"
            prop="orderState"
            align="center"
            :formatter="orderStateFormat"
          />
          <el-table-column
            label="申请日期"
            prop="applyTime"
            align="center"
            width="160"
          />
          <el-table-column
            label="要款日期"
            prop="collectTime"
            align="center"
            width="160"
          />
          <el-table-column
            label="放款日期"
            prop="loanTime"
            align="center"
            width="160"
          />
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />

        <template v-if="currentRow">
          <div class="more-box">
            <div class="no">
              操作订单：{{ currentRow.orderId }}
            </div>
            <div>
              <el-button
                round
                type="primary"
                plain
                @click="handleLookFace"
              >
                查看人脸
              </el-button>
              <el-button
                round
                :type="stage ? 'success' : 'info'"
                @click="handleStage"
              >
                查看进度
              </el-button>
              <el-button
                round
                :type="plan ? 'success' : 'info'"
                @click="handlePlan"
              >
                还款计划
              </el-button>
              <el-button
                round
                type="primary"
                plain
                @click="handleResetPW"
              >
                重置密码
              </el-button>
            </div>
          </div>
        </template>

        <!-- 进度 -->
        <template v-if="stage">
          <el-steps style="margin-bottom: 16px;">
            <el-step
              v-for="(item, index) in stage"
              :key="index"
              :title="item.stageState"
              :description="item.stageStateName"
            />
          </el-steps>
        </template>

        <!-- 还款计划 -->
        <template v-if="plan">
          <el-table
            border="border"
            :data="plan"
          >
            <el-table-column
              label="期数"
              prop="period"
              align="center"
            />
            <el-table-column
              label="应还时间"
              prop="planRepayDate"
              align="center"
            />
            <el-table-column
              label="应还总额"
              prop="amount"
              align="center"
            />
            <el-table-column
              label="应还本金"
              prop="principalAmt"
              align="center"
            />
            <el-table-column
              label="应还利息"
              prop="interestAmt"
              align="center"
            />
            <el-table-column
              label="应还融担费"
              prop="guaranteeAmt"
              align="center"
            />
            <el-table-column
              label="应还咨询服务费"
              prop="consultFee"
              align="center"
            />
            <el-table-column
              label="应还总罚息"
              prop="penaltyTotalAmt"
              align="center"
            />
            <el-table-column
              label="应还资方罚息"
              prop="penaltyBankAmt"
              align="center"
            />
            <el-table-column
              label="应还平台罚息"
              prop="penaltyAmt"
              align="center"
            />
            <el-table-column
              label="当期状态"
              prop="nowRepayState"
              align="center"
            />
            <el-table-column
              label="逾期天数"
              prop="overdueDay"
              align="center"
            />
            <el-table-column
              label="还款状态"
              prop="custRepayState"
              align="center"
            />
            <el-table-column
              label="实还时间"
              prop="actRepayTime"
              align="center"
            />
            <el-table-column
              label="实还总额"
              prop="actAmount"
              align="center"
            />
            <el-table-column
              label="减免金额"
              prop="reduceAmount"
              align="center"
            />
            <el-table-column
              label="溢出金额"
              prop="overflowAmount"
              align="center"
            />
            <el-table-column
              label="还款方式"
              prop="repayMode"
              align="center"
            />
          </el-table>
        </template>
      </el-tab-pane>

      <el-tab-pane :disabled="!currentRow" label="投诉/备注" name="second">
        <complaint ref="complaint" :currentRow="currentRow" />
        <!-- <div class="title-box">
          <span>客户投诉</span>
          <el-button
            type="text"
            @click="handleAddComplaint"
          >
            新增投诉
          </el-button>
        </div>
        <el-table
          border="border"
          style="margin-bottom: 16px;"
          :data="customerComplainList"
        >
          <el-table-column
            label="订单编号"
            prop="orderNo"
            align="center"
          />
          <el-table-column
            label="性别"
            prop="gender"
            align="center"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.gender === 'MALE'">男</span>
              <span v-if="scope.row.gender === 'FEMALE'">女</span>
            </template>
          </el-table-column>
          <el-table-column
            label="投诉渠道"
            prop="complainChannel"
            align="center"
          />
          <el-table-column
            label="姓名"
            prop="name"
            align="center"
          />
          <el-table-column
            label="贷款金额"
            prop="loanAmount"
            align="center"
          />
          <el-table-column
            label="投诉类型"
            prop="complainType"
            align="center"
          />
          <el-table-column
            label="主要事项"
            prop="mainItem"
            align="center"
          />
          <el-table-column
            label="链接"
            prop="channelUrl"
            align="center"
          />
          <el-table-column
            label="是否逾期"
            prop="overdueState"
            align="center"
          />
          <el-table-column
            label="处理时间"
            prop="completeTime"
            align="center"
          />
          <el-table-column
            label="协助人员"
            prop="assistPerson"
            align="center"
          />
          <el-table-column
            label="创建人"
            prop="createdBy"
            align="center"
          />
          <el-table-column
            label="创建时间"
            prop="createdTime"
            align="center"
          />
          <el-table-column
            label="预约处理时间"
            prop="subscribeResolveTime"
            align="center"
          />
          <el-table-column
            label="完成时间"
            prop="completeTime"
            align="center"
          />
          <el-table-column
            label="关怀金/减免金额"
            prop="reduceAmt"
            align="center"
          />
          <el-table-column
            label="是否停催"
            prop="discontinuationState"
            align="center"
          />
          <el-table-column
            label="操作"
            align="center"
          >
            <template slot-scope="scope">
              <el-button
                type="text"
                @click="handleEditeComplaint(scope.row)"
              >
                编辑
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="title-box">
          <span>订单备注</span>
          <el-button
            type="text"
            @click="handleAddRemark"
          >
            新增备注
          </el-button>
        </div>
        <el-table
          border="border"
          :data="orderRemarkList"
        >
          <el-table-column
            label="订单编号"
            prop="orderNo"
            align="center"
          />
          <el-table-column
            label="操作账户"
            prop="operateAccount"
            align="center"
          />
          <el-table-column
            label="类型"
            prop="remarkType"
            align="center"
          />
          <el-table-column
            label="备注"
            prop="remark"
            align="center"
          />
          <el-table-column
            label="创建时间"
            prop="createdTime"
            align="center"
          />
          <el-table-column
            label="更新时间"
            prop=""
            align="center"
          />
        </el-table> -->
      </el-tab-pane>

      <el-tab-pane :disabled="!currentRow" label="短信记录" name="third">
        <div class="title-box">
          <span>短信</span>
          <el-button
            type="text"
            @click="openSmsDialog"
          >
            发送短信
          </el-button>
        </div>
        <el-table
          border="border"
          :data="smsList"
        >
          <el-table-column
            label="手机号"
            prop="mobile"
            align="center"
          />
          <el-table-column
            label="短信类型"
            prop="messageType"
            align="center"
          />
          <el-table-column
            label="短信内容"
            prop="messageContent"
            align="center"
          />
          <el-table-column
            label="发送渠道"
            prop="sendChannel"
            align="center"
          />
          <el-table-column
            label="发送时间"
            prop="createdTime"
            align="center"
          />
        </el-table>
      </el-tab-pane>
    </el-tabs>

    <!-- 客户信息 -->
    <el-dialog
      title="客户信息"
      :visible.sync="customerInfoDialogVisible"
      width="500px"
    >
      <el-descriptions
        v-if="customerInfo"
        :column="1"
        border
      >
        <el-descriptions-item>
          <template slot="label">
            <i class="el-icon-user" />
            客户姓名
          </template>
          <span class="right-text">{{ customerInfo.name }}</span>
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            <i class="el-icon-magic-stick" />
            客户性别
          </template>
          <span
            v-if="customerInfo.gender === 'MALE'"
            class="right-text"
          >男</span>
          <span
            v-if="customerInfo.gender === 'FEMALE'"
            class="right-text"
          >女</span>
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            <i class="el-icon-paperclip" />
            注册渠道
          </template>
          <span class="right-text">{{ customerInfo.registerChannel }}</span>
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            <i class="el-icon-mobile" />
            手机号码
          </template>
          <span class="right-text">{{ customerInfo.mobile }}</span>
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            <i class="el-icon-wallet" />
            证件号码
          </template>
          <span class="right-text">{{ customerInfo.certNo }}</span>
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            <i class="el-icon-date" />
            注册日期
          </template>
          <span class="right-text">{{ customerInfo.registerDate }}</span>
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            <i class="el-icon-coin" />
            最后登录
          </template>
          <span class="right-text">{{ customerInfo.lastLoginDate }}</span>
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            <i class="el-icon-coin" />
            银行卡信息
          </template>
          <span class="right-text">{{ customerInfo.bankCardNo }}({{ customerInfo.bankName }})</span>
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            <i class="el-icon-coin" />
            单位
          </template>
          <span class="right-text">{{ customerInfo.unit }}</span>
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            <i class="el-icon-coin" />
            单位地址
          </template>
          <span class="right-text">{{ customerInfo.unitAddress }}</span>
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            <i class="el-icon-coin" />
            单位电话
          </template>
          <span class="right-text">{{ customerInfo.unitPhone }}</span>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 人脸信息 -->
    <el-dialog
      title="人脸信息"
      :visible.sync="faceDialogVisible"
      width="700px"
    >
      <img
        v-if="face"
        style="display: block; width: 100%; height: auto;"
        :src="face.url"
      >
    </el-dialog>

    <!-- 新增\编辑投诉 -->
    <el-dialog
      :title="mode === 'add' ? '新增投诉' : '编辑投诉'"
      :visible.sync="complaintDialogVisible"
      width="500px"
      :before-close="closeComplaint"
    >
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        :rules="rules"
        label-width="110px"
      >
        <el-form-item
          v-if="mode === 'add'"
          label="姓名"
          prop="name"
        >
          <el-input
            v-model="ruleForm.name"
            disabled
          />
        </el-form-item>
        <el-form-item
          v-if="mode === 'add'"
          label="性别"
          prop="gender"
        >
          <el-select
            v-model="ruleForm.gender"
            placeholder="请选择"
            style="width:100%"
            disabled
          >
            <el-option
              label="男"
              value="MALE"
            />
            <el-option
              label="女"
              value="FEMALE"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="手机号"
          prop="mobile"
        >
          <el-input
            v-model="ruleForm.mobile"
            :disabled="mode === 'add'"
          />
        </el-form-item>
        <el-form-item
          v-if="mode === 'add'"
          label="订单号"
          prop="orderNo"
        >
          <el-input
            v-model="ruleForm.orderNo"
            disabled
          />
        </el-form-item>
        <el-form-item
          v-if="mode === 'add'"
          label="申请时间"
          prop="applyTime"
        >
          <el-input
            v-model="ruleForm.applyTime"
            disabled
          />
        </el-form-item>
        <el-form-item
          v-if="mode === 'add'"
          label="资方渠道"
          prop="bankChannel"
        >
          <el-input
            v-model="ruleForm.bankChannel"
            disabled
          />
        </el-form-item>
        <el-form-item
          v-if="mode === 'add'"
          label="贷款金额"
          prop="loanAmount"
        >
          <el-input
            v-model="ruleForm.loanAmount"
            disabled
          />
        </el-form-item>
        <el-form-item
          v-if="mode === 'add'"
          label="是否逾期"
          prop="overdueState"
        >
          <el-select
            v-model="ruleForm.overdueState"
            placeholder="请选择"
            style="width:100%"
          >
            <el-option
              label="是"
              value="是"
            />
            <el-option
              label="否"
              value="否"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="处理状态" prop="">
          <el-select placeholder="请选择" style="width:100%">
            <el-option label="待跟进" value="待跟进"></el-option>
            <el-option label="协商中" value="协商中"></el-option>
            <el-option label="申请撤案" value="申请撤案"></el-option>
            <el-option label="已解决" value="已解决"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item
          label="投诉渠道"
          prop="complainChannel"
        >
          <el-input v-model="ruleForm.complainChannel" />
        </el-form-item>
        <el-form-item
          label="渠道链接"
          prop="channelUrl"
        >
          <el-input v-model="ruleForm.channelUrl" />
        </el-form-item>
        <el-form-item
          label="投诉类型"
          prop="complainType"
        >
          <el-select
            v-model="ruleForm.complainType"
            multiple
            placeholder="请选择"
            style="width:100%"
          >
            <el-option
              label="业务流程问题"
              value="业务流程问题"
            />
            <el-option
              label="费率问题"
              value="费率问题"
            />
            <el-option
              label="提前结清费用问题"
              value="提前结清费用问题"
            />
            <el-option
              label="权益使用问题"
              value="权益使用问题"
            />
            <el-option
              label="催收问题"
              value="催收问题"
            />
            <el-option
              label="信息安全问题"
              value="信息安全问题"
            />
            <el-option
              label="合同发票"
              value="合同发票"
            />
            <el-option
              label="其他问题"
              value="其他问题"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="主要事项"
          prop="mainItem"
        >
          <el-input
            v-model="ruleForm.mainItem"
            type="textarea"
            :rows="4"
          />
        </el-form-item>
        <el-form-item
          label="投诉备注"
          prop="remark"
        >
          <el-input
            v-model="ruleForm.remark"
            type="textarea"
            :rows="4"
          />
        </el-form-item>
        <el-form-item
          v-if="mode === 'edite'"
          label="协助人员"
          prop="assistPerson"
        >
          <el-input v-model="ruleForm.assistPerson" />
        </el-form-item>
        <el-form-item
          label="预约处理时间"
          prop="subscribeResolveTime"
        >
          <el-date-picker
            v-model="ruleForm.subscribeResolveTime"
            type="datetime"
            style="width:100%"
            placeholder="选择日期时间"
          />
        </el-form-item>
        <el-form-item
          label="完成时间"
          prop="completeTime"
        >
          <el-date-picker
            v-model="ruleForm.completeTime"
            type="datetime"
            style="width:100%"
            placeholder="选择日期时间"
          />
        </el-form-item>
        <!-- <el-form-item label="是否解决" prop="resolveState" v-if="mode === 'edite'">
          <el-select v-model="ruleForm.resolveState" placeholder="请选择" style="width:100%">
            <el-option label="是" value="是"></el-option>
            <el-option label="否" value="否"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item
          v-if="mode === 'edite'"
          label="处理状态"
          prop="handleState"
        >
          <el-select
            v-model="ruleForm.handleState"
            placeholder="请选择"
            style="width:100%"
          >
            <el-option
              label="待跟进"
              value="INIT"
            />
            <el-option
              label="协商中"
              value="PROCESSING"
            />
            <el-option
              label="申请撤销"
              value="CANCELED"
            />
            <el-option
              label="已解决"
              value="RESOLVED"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="mode === 'edite'"
          label="关怀金/减免金额"
          prop="reduceAmt"
        >
          <el-input v-model="ruleForm.reduceAmt" />
        </el-form-item>

        <el-form-item
          label="是否停催"
          prop="discontinuationState"
        >
          <el-select
            v-model="ruleForm.discontinuationState"
            placeholder="请选择"
            style="width:100%"
          >
            <el-option
              label="是"
              value="是"
            />
            <el-option
              label="否"
              value="否"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            round
            @click="closeComplaint"
          >
            取消
          </el-button>
          <el-button
            round
            type="primary"
            :loading="submiting"
            @click="submitForm"
          >
            确认
          </el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 新增备注 -->
    <el-dialog
      title="新增备注"
      :visible.sync="remarkDialogVisible"
      width="500px"
      :before-close="closeRemark"
    >
      <el-form
        ref="ruleFormRemark"
        :model="ruleFormRemark"
        :rules="rulesRemark"
        label-width="100px"
      >
        <el-form-item
          label="订单编号"
          prop="orderNo"
        >
          <el-input
            v-model="ruleFormRemark.orderNo"
            disabled
          />
        </el-form-item>
        <el-form-item
          label="客户手机"
          prop="mobile"
        >
          <el-input
            v-model="ruleFormRemark.mobile"
            disabled
          />
        </el-form-item>
        <el-form-item
          label="客户姓名"
          prop="name"
        >
          <el-input
            v-model="ruleFormRemark.name"
            disabled
          />
        </el-form-item>
        <el-form-item
          label="资金渠道"
          prop="bankChannel"
        >
          <el-input
            v-model="ruleFormRemark.bankChannel"
            disabled
          />
        </el-form-item>
        <el-form-item
          label="备注类型"
          prop="remarkType"
        >
          <el-select
            v-model="ruleFormRemark.remarkType"
            placeholder="请选择"
            style="width:100%"
          >
            <el-option
              label="订单备注"
              value="订单备注"
            />
            <el-option
              label="工单备注"
              value="工单备注"
            />
            <el-option
              label="催收备注"
              value="催收备注"
            />
            <el-option
              label="投诉备注"
              value="投诉备注"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="备注定义"
          prop="remarkDefine"
        >
          <el-select
            v-model="ruleFormRemark.remarkDefine"
            placeholder="请选择"
            style="width:100%"
          >
            <el-option
              label="贷款业务咨询"
              value="贷款业务咨询"
            />
            <el-option
              label="审批放款查询"
              value="审批放款查询"
            />
            <el-option
              label="异常反馈"
              value="异常反馈"
            />
            <el-option
              label="沟通还款"
              value="沟通还款"
            />
            <el-option
              label="投诉争议"
              value="投诉争议"
            />
            <el-option
              label="无效通话"
              value="无效通话"
            />
            <el-option
              label="帮助建议"
              value="帮助建议"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="订单备注"
          prop="remark"
        >
          <el-input
            v-model="ruleFormRemark.remark"
            type="textarea"
            :rows="4"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            round
            @click="closeRemark"
          >
            取消
          </el-button>
          <el-button
            round
            type="primary"
            @click="submitFormRemark"
          >
            确认
          </el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 发送短信 -->
    <el-dialog
      title="发送贷前短信"
      :visible.sync="dialogVisibleSms"
      width="500px"
      :before-close="closeSms"
    >
      <el-form
        ref="ruleFormSms"
        :model="ruleFormSms"
        :rules="rulesSms"
        label-width="110px"
      >
        <el-form-item
          label="短信列表"
          prop="messageType"
        >
          <el-select
            v-model="ruleFormSms.messageType"
            placeholder="请选择短信模板"
            style="width:100%"
            @change="tempChange"
          >
            <el-option
              v-for="item in template"
              :label="item.messageName"
              :value="item.messageType"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="模板内容">
          <div
            v-if="smsText"
            class="text-box"
          >
            {{ smsText }}
          </div>
        </el-form-item>
        <el-form-item>
          <el-button
            round
            @click="closeSms"
          >
            取消
          </el-button>
          <el-button
            round
            type="primary"
            @click="sendSms"
          >
            确认
          </el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  orderQuery,
  orderCustom,
  orderFace,
  orderPlan,
  orderStage,
  resetPassword
} from '@/api/order'
import { complaintNote, addComplaint, updateComplaint, addOrderRemark } from '@/api/customer'
import { queryRecord, queryTemplate, sendMessage } from '@/api/sms'
import { get as getDictByName } from '@/api/system/dictDetail'
import complaint from './components/complaint.vue'

export default {
  name: 'OrderInfo',
  components: {
    complaint
  },
  data() {
    return {
      // tab
      activeName: 'first',
      // 查询参数
      queryParams: {
        type: 'mobile',
        typeText: '',
        pageNum: 1,
        pageSize: 10
      },
      loading: false,
      list: [],
      total: 0,

      // 当前选中的订单
      currentRow: undefined,
      // 客户信息
      customerInfo: {},
      customerInfoDialogVisible: false,
      // 人脸信息
      face: undefined,
      faceDialogVisible: false,
      // 还款计划
      plan: undefined,
      // 进度
      stage: undefined,
      // 投诉、备注
      customerComplainList: [],
      orderRemarkList: [],

      // 投诉弹窗
      mode: '',
      complaintDialogVisible: false,
      ruleForm: {},
      rules: {
        name: [{ required: true, message: '姓名不能为空', trigger: 'blur' }],
        mobile: [{ required: true, message: '手机号不能为空', trigger: 'change' }],
        orderNo: [{ required: true, message: '订单号不能为空', trigger: 'change' }],
        bankChannel: [{ required: true, message: '资方渠道不能为空', trigger: 'change' }],
        complainChannel: [{ required: true, message: '投诉渠道不能为空', trigger: 'change' }],
        mainItem: [{ required: true, message: '主要事项不能为空', trigger: 'change' }],
        applyTime: [{ required: true, message: '申请时间不能为空', trigger: 'change' }],
        loanAmount: [{ required: true, message: '贷款金额不能为空', trigger: 'change' }],
        gender: [{ required: true, message: '性别不能为空', trigger: 'change' }]
      },
      submiting: false,

      // 备注弹窗
      remarkDialogVisible: false,
      ruleFormRemark: {},
      rulesRemark: {
        name: [{ required: true, message: '姓名不能为空', trigger: 'blur' }],
        mobile: [{ required: true, message: '手机号不能为空', trigger: 'change' }],
        orderNo: [{ required: true, message: '订单号不能为空', trigger: 'change' }],
        // bankChannel: [{ required: true, message: "资方渠道不能为空", trigger: "change" }],
        remark: [{ required: true, message: '订单备注不能为空', trigger: 'change' }]
      },

      // 短信
      smsList: [],
      dialogVisibleSms: false,
      ruleFormSms: {},
      rulesSms: {
        messageType: [{ required: true, message: '短信列表不能为空', trigger: 'blur' }]
      },
      smsText: '',
      template: [],

      // 字典
      flowChannelOptions: [],
      orderStateOptions: [],
      bankChannelOptions: [],
      rightsPayChannelOptions: []
    }
  },
  created() {
    getDictByName('bankChannel').then(res => {
      this.bankChannelOptions = res.content
    })

    getDictByName('flowChannel').then(res => {
      this.flowChannelOptions = res.content
    })

    getDictByName('orderState').then(res => {
      this.orderStateOptions = res.content
    })

    getDictByName('payChannel').then(res => {
      this.rightsPayChannelOptions = res.content
    })
  },
  methods: {
    // 重置密码
    handleResetPW() {
      this.$confirm('是否确认重置密码?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        roundButton: true,
        type: 'warning'
      }).then(() => {
        const params = {
          mobile: this.currentRow.mobile
        }
        resetPassword(params).then(res => {
          this.$alert(`重置密码成功！新密码${res.data}`, '提示', {
            confirmButtonText: '确定'
          })
        })
      })
    },
    // tab点击
    handleClick(tab, event) {
      if (tab.paneName === 'first') {
        return
      }

      if (tab.paneName === 'second') {
        if (!this.currentRow) {
          this.$message.warning('请先选择1个订单')
          return
        }
        this.$refs.complaint.getComplaintNote()
        this.$refs.complaint.getCallRecord()
      }

      if (tab.paneName === 'third') {
        if (!this.currentRow) {
          this.$message.warning('请先选择1个订单')
          return
        }
        this.getSmsRecord()
      }
    },

    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 外呼
    handlePhoneCall() {
      // 如果没有选中订单，不允许拨打
      if (!this.currentRow) {
        this.$message.warning('请先选择1个订单')
        return
      }

      this.$refs.complaint.openPhone()
    },
    // 投诉备注

    // 获取订单列表
    getList() {
      if (this.queryParams.typeText === '') {
        this.$message.warning('请输入搜索内容')
        return
      }

      this.loading = true
      const params = {
        mobile:
          this.queryParams.type === 'mobile'
            ? this.queryParams.typeText
            : undefined,
        orderId:
          this.queryParams.type === 'orderId'
            ? this.queryParams.typeText
            : undefined,
        certNo:
          this.queryParams.type === 'certNo'
            ? this.queryParams.typeText
            : undefined,
        outerOrderId:
          this.queryParams.type === 'outerOrderId'
            ? this.queryParams.typeText
            : undefined,
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize
      }
      orderQuery(params).then(res => {
        this.currentRow = undefined
        this.plan = undefined
        this.stage = undefined

        this.list = res.data.list
        this.total = res.data.total
        this.loading = false
      })
    },

    // 点击更多
    choose(row) {
      this.currentRow = row
    },

    // 查看客户信息
    handleCustomerInfo(userId, orderId) {
      orderCustom({ userId, orderId }).then(res => {
        this.customerInfo = res.data
        this.customerInfoDialogVisible = true
      })
    },

    // 查看人脸
    handleLookFace() {
      orderFace({ userId: this.currentRow.userId }).then(res => {
        this.face = res.data
        this.faceDialogVisible = true
      })
    },

    // 查看进度
    handleStage() {
      if (this.stage) {
        this.stage = undefined
      } else {
        orderStage({ orderId: this.currentRow.orderId }).then(res => {
          this.stage = res.data
        })
      }
    },

    // 还款计划
    handlePlan() {
      if (this.plan) {
        this.plan = undefined
      } else {
        orderPlan({ orderId: this.currentRow.orderId }).then(res => {
          this.plan = res.data.repayPlanRsps
        })
      }
    },

    // 获取订单相关投诉、备注
    getComplaintNote() {
      complaintNote({ orderId: this.currentRow.orderId }).then(res => {
        this.customerComplainList = res.data.customerComplainList || []
        this.orderRemarkList = res.data.orderRemarkLis || []
      })
    },

    // 新增投诉
    handleAddComplaint() {
      if (!this.currentRow) {
        this.$message.warning('请先选择1个订单')
        return
      }
      this.mode = 'add'
      this.ruleForm = {
        orderNo: this.currentRow.orderId, // 订单编号
        name: this.currentRow.name, // 用户姓名
        mobile: this.currentRow.mobile, // 手机号
        bankChannel: this.currentRow.bankChannel, //	资金方
        gender: this.currentRow.gender, // 性别
        loanAmount: this.currentRow.applyAmount, // 贷款金额
        complainChannel: undefined, //	投诉渠道
        complainType: undefined, //	投诉类型
        mainItem: undefined, //	主要事项
        remark: undefined, //	投诉备注
        channelUrl: undefined, //	渠道链接
        overdueState: undefined, // 是否逾期
        applyTime: this.currentRow.applyTime, // 申请时间
        subscribeResolveTime: undefined, //	预约处理时间
        discontinuationState: undefined //	是否停催
      }
      this.complaintDialogVisible = true
    },

    // 编辑投诉
    handleEditeComplaint(row) {
      console.log(row)

      this.mode = 'edite'
      this.ruleForm = {
        id: row.id, //	投诉id
        mobile: row.mobile, //	手机号
        complainChannel: row.complainChannel, //	投诉渠道
        channelUrl: row.channelUrl, //	渠道链接
        complainType: row.complainType ? row.complainType.split(',') : [], //	投诉类型
        mainItem: row.mainItem, //	主要事项
        remark: row.remark, //	投诉备注
        subscribeResolveTime: row.subscribeResolveTime, //	预约处理时间
        discontinuationState: row.discontinuationState, //	是否停催
        assistPerson: row.assistPerson, // 协助人员
        completeTime: row.completeTime, // 完成时间
        handleState: row.handleState, // 处理状态
        reduceAmt: row.reduceAmt // 关怀金/减免金额
      }
      this.complaintDialogVisible = true
    },

    // 提交投诉
    submitForm() {
      this.$refs['ruleForm'].validate(valid => {
        if (valid) {
          const params = {
            ...this.ruleForm,
            complainType: this.ruleForm.complainType ? this.ruleForm.complainType.join(',') : undefined
          }

          this.submiting = true

          if (this.mode === 'add') {
            addComplaint(params).then(res => {
              this.$message.success('新增投诉成功！')
              this.closeComplaint()
              this.getComplaintNote()
            }).finally(() => {
              this.submiting = false
            })
          } else if (this.mode === 'edite') {
            updateComplaint(params).then(res => {
              this.$message.success('编辑投诉成功！')
              this.closeComplaint()
              this.getComplaintNote()
            }).finally(() => {
              this.submiting = false
            })
          }
        }
      })
    },

    // 关闭投诉弹窗
    closeComplaint() {
      this.complaintDialogVisible = false
      this.ruleForm = {}
      this.$refs['ruleForm'].resetFields()
    },

    // 新增备注
    handleAddRemark() {
      if (!this.currentRow) {
        this.$message.warning('请先选择1个订单')
        return
      }
      this.ruleFormRemark = {
        orderNo: this.currentRow.orderId, // 订单编号
        name: this.currentRow.name, // 用户姓名
        mobile: this.currentRow.mobile, // 手机号
        bankChannel: this.currentRow.bankChannel, //	资金渠道
        remarkType: undefined, // 备注类型
        remarkDefine: undefined, //	备注定义
        remark: undefined //	订单备注
      }
      this.remarkDialogVisible = true
    },

    // 提交备注
    submitFormRemark() {
      this.$refs['ruleFormRemark'].validate(valid => {
        if (valid) {
          addOrderRemark(this.ruleFormRemark).then(res => {
            this.$message.success('新增备注成功！')
            this.closeRemark()
            this.getComplaintNote()
          })
        }
      })
    },

    // 关闭备注弹窗
    closeRemark() {
      this.remarkDialogVisible = false
      this.ruleFormRemark = {}
      this.$refs['ruleFormRemark'].resetFields()
    },

    // 短信查询
    getSmsRecord() {
      queryRecord({ orderId: this.currentRow.orderId }).then(res => {
        this.smsList = res.data
      })
    },

    // 打开发送短信弹窗
    openSmsDialog() {
      this.dialogVisibleSms = true
      this.smsListChange()
    },

    // 关闭发送短信弹窗
    closeSms() {
      this.dialogVisibleSms = false
      this.ruleFormSms = {}
      this.smsText = ''
      this.$refs['ruleFormSms'].resetFields()
    },

    // 发送短信
    sendSms() {
      this.$refs['ruleFormSms'].validate(valid => {
        if (valid) {
          const params = {
            mobile: this.currentRow.mobile, // 手机号
            orderNo: this.currentRow.orderId, // 订单编号
            messageType: this.ruleFormSms.messageType,
            messageContent: this.smsText
          }
          sendMessage(params).then(res => {
            this.$message.success('发送成功！')
            this.closeSms()
            this.getSmsRecord()
          })
        }
      })
    },

    // 查询短信模版内容
    smsListChange() {
      const params = {
        orderId: this.currentRow.orderId
      }
      queryTemplate(params).then(res => {
        this.template = res.data
      })
    },

    tempChange() {
      const obj = this.template.find(item => item.messageType === this.ruleFormSms.messageType)
      this.smsText = obj.messageContent
    },

    flowChannelFormat({ flowChannel }) {
      const obj = this.flowChannelOptions.find(item => item.value === flowChannel)
      return obj ? obj.label : flowChannel
    },

    bankChannelFormat({ bankChannel }) {
      const obj = this.bankChannelOptions.find(item => item.value === bankChannel)
      return obj ? obj.label : bankChannel
    },

    orderStateFormat({ orderState }) {
      const obj = this.orderStateOptions.find(item => item.value === orderState)
      return obj ? obj.label : '--'
    },

    rightsPayChannelFormat({ rightsPayChannel }) {
      const obj = this.rightsPayChannelOptions.find(item => item.value === rightsPayChannel)
      return obj ? obj.label : '--'
    }

  }
}
</script>

<style scoped>
.more-box {
  padding-top: 16px;
  margin-bottom: 16px;
}

.no {
  font-size: 15px;
  margin-bottom: 10px;
}

.right-text {
  display: block;
  width: 290px;
}

.title-box span {
  font-size: 14px;
  font-weight: 500;
  display: inline-block;
  width: 200px;
}

.text-box {
  padding: 10px;
  border: #e5e5e5 1px solid;
  background: #f5f5f5;
  line-height: 1.4;
}
</style>
