<template>
  <div class="complaint-remark">
    <!-- 客户投诉 -->
    <el-row>
      <div class="title-box">
        <span>客户投诉</span>
        <el-button type="text" @click="handleAddComplaint">
          新增投诉
        </el-button>
      </div>
      <el-table border="border" style="margin-bottom: 16px;" :data="customerComplainList">
        <el-table-column label="订单编号" prop="orderNo" align="center" />
        <el-table-column label="性别" prop="gender" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.gender === 'MALE'">男</span>
            <span v-if="scope.row.gender === 'FEMALE'">女</span>
          </template>
        </el-table-column>
        <el-table-column label="投诉渠道" prop="complainChannel" align="center" />
        <el-table-column label="姓名" prop="name" align="center" />
        <el-table-column label="贷款金额" prop="loanAmount" align="center" />
        <el-table-column label="投诉类型" prop="complainType" align="center" />
        <el-table-column label="主要事项" prop="mainItem" align="center" />
        <el-table-column label="链接" prop="channelUrl" align="center" />
        <el-table-column label="是否逾期" prop="overdueState" align="center" />
        <!-- <el-table-column label="是否解决" prop="resolveState" align="center"></el-table-column> -->
        <el-table-column label="处理时间" prop="completeTime" align="center" />
        <el-table-column label="协助人员" prop="assistPerson" align="center" />
        <el-table-column label="创建人" prop="createdBy" align="center" />
        <el-table-column label="创建时间" prop="createdTime" align="center" />
        <el-table-column label="预约处理时间" prop="subscribeResolveTime" align="center" />
        <el-table-column label="完成时间" prop="completeTime" align="center" />
        <el-table-column label="关怀金/减免金额" prop="reduceAmt" align="center" />
        <el-table-column label="是否停催" prop="discontinuationState" align="center" />
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-button type="text" @click="handleEditeComplaint(scope.row)">
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-row>

    <!-- 订单备注 -->
    <el-row>
      <div class="title-box">
        <span>订单备注</span>
        <el-button type="text" @click="handleAddRemark">
          新增备注
        </el-button>
      </div>
      <el-table border="border" :data="orderRemarkList">
        <el-table-column label="订单编号" prop="orderNo" align="center" />
        <el-table-column label="操作账户" prop="operateAccount" align="center" />
        <el-table-column label="类型" prop="remarkType" align="center" />
        <el-table-column label="备注" prop="remark" align="center" />
        <el-table-column label="创建时间" prop="createdTime" align="center" />
        <el-table-column label="更新时间" prop="" align="center" />
      </el-table>
    </el-row>

    <!-- 客诉回访 -->
    <el-row>
      <div class="title-box">
        <p><span>客诉回访</span></p>
        <!-- <el-button type="text" @click="openPhone"> 新增备注 </el-button> -->
      </div>
      <el-table border :data="customerCallList">
        <el-table-column label="订单编号" prop="orderId" align="center" width="380px" />
        <el-table-column label="接听情况" align="center" width="150px">
          <template slot-scope="scope">
            <dict-tag :value="scope.row.callState" :options="dict.callState" />
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="description" align="center" />
        <el-table-column label="创建人" prop="createBy" align="center" width="150px" />
        <el-table-column label="创建时间" prop="createTime" align="center" width="180px" />
      </el-table>
    </el-row>

    <!-- 新增\编辑投诉 -->
    <el-dialog :title="titleVal" :visible.sync="complaintDialogVisible" width="500px"
      :before-close="closeComplaint">
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="110px">
        <el-form-item v-if="mode === 'add'" label="姓名" prop="name">
          <el-input v-model="ruleForm.name" disabled />
        </el-form-item>
        <el-form-item v-if="mode === 'add'" label="性别" prop="gender">
          <el-select v-model="ruleForm.gender" placeholder="请选择" style="width:100%" disabled>
            <el-option label="男" value="MALE" />
            <el-option label="女" value="FEMALE" />
          </el-select>
        </el-form-item>
        <el-form-item label="手机号" prop="mobile">
          <el-input v-model="ruleForm.mobile" :disabled="mode === 'add'" />
        </el-form-item>
        <el-form-item v-if="mode === 'add'" label="订单号" prop="orderNo">
          <el-input v-model="ruleForm.orderNo" disabled />
        </el-form-item>
        <el-form-item v-if="mode === 'add'" label="申请时间" prop="applyTime">
          <el-input v-model="ruleForm.applyTime" disabled />
        </el-form-item>
        <el-form-item v-if="mode === 'add'" label="资方渠道" prop="bankChannel">
          <el-input v-model="ruleForm.bankChannel" disabled />
        </el-form-item>
        <el-form-item v-if="mode === 'add'" label="贷款金额" prop="loanAmount">
          <el-input v-model="ruleForm.loanAmount" disabled />
        </el-form-item>
        <el-form-item v-if="mode === 'add'" label="是否逾期" prop="overdueState">
          <el-select v-model="ruleForm.overdueState" placeholder="请选择" style="width:100%">
            <el-option label="是" value="是" />
            <el-option label="否" value="否" />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="处理状态" prop="">
          <el-select placeholder="请选择" style="width:100%">
            <el-option label="待跟进" value="待跟进"></el-option>
            <el-option label="协商中" value="协商中"></el-option>
            <el-option label="申请撤案" value="申请撤案"></el-option>
            <el-option label="已解决" value="已解决"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="投诉渠道" prop="complainChannel">
          <el-input v-model="ruleForm.complainChannel" />
        </el-form-item>
        <el-form-item label="渠道链接" prop="channelUrl">
          <el-input v-model="ruleForm.channelUrl" />
        </el-form-item>
        <el-form-item label="投诉类型" prop="complainType">
          <el-select v-model="ruleForm.complainType" multiple placeholder="请选择" style="width:100%">
            <el-option label="业务流程问题" value="业务流程问题" />
            <el-option label="费率问题" value="费率问题" />
            <el-option label="提前结清费用问题" value="提前结清费用问题" />
            <el-option label="权益使用问题" value="权益使用问题" />
            <el-option label="催收问题" value="催收问题" />
            <el-option label="信息安全问题" value="信息安全问题" />
            <el-option label="合同发票" value="合同发票" />
            <el-option label="其他问题" value="其他问题" />
          </el-select>
        </el-form-item>
        <el-form-item label="主要事项" prop="mainItem">
          <el-input v-model="ruleForm.mainItem" type="textarea" :rows="4" />
        </el-form-item>
        <el-form-item label="投诉备注" prop="remark">
          <el-input v-model="ruleForm.remark" type="textarea" :rows="4" />
        </el-form-item>
        <el-form-item v-if="mode === 'edite'" label="协助人员" prop="assistPerson">
          <el-input v-model="ruleForm.assistPerson" />
        </el-form-item>
        <el-form-item label="预约处理时间" prop="subscribeResolveTime">
          <el-date-picker v-model="ruleForm.subscribeResolveTime" type="datetime" style="width:100%"
            placeholder="选择日期时间" />
        </el-form-item>
        <el-form-item label="完成时间" prop="completeTime">
          <el-date-picker v-model="ruleForm.completeTime" type="datetime" style="width:100%" placeholder="选择日期时间" />
        </el-form-item>
        <!-- <el-form-item label="是否解决" prop="resolveState" v-if="mode === 'edite'">
          <el-select v-model="ruleForm.resolveState" placeholder="请选择" style="width:100%">
            <el-option label="是" value="是"></el-option>
            <el-option label="否" value="否"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item v-if="mode === 'edite'" label="处理状态" prop="handleState">
          <el-select v-model="ruleForm.handleState" placeholder="请选择" style="width:100%">
            <el-option label="待跟进" value="INIT" />
            <el-option label="协商中" value="PROCESSING" />
            <el-option label="申请撤销" value="CANCELED" />
            <el-option label="已解决" value="RESOLVED" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="mode === 'edite'" label="关怀金/减免金额" prop="reduceAmt">
          <el-input v-model="ruleForm.reduceAmt" />
        </el-form-item>

        <el-form-item label="是否停催" prop="discontinuationState">
          <el-select v-model="ruleForm.discontinuationState" placeholder="请选择" style="width:100%">
            <el-option label="是" value="是" />
            <el-option label="否" value="否" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button round @click="closeComplaint">
            取消
          </el-button>
          <el-button round type="primary" :loading="submiting" @click="submitForm">
            确认
          </el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 新增备注 -->
    <el-dialog title="新增备注" :visible.sync="remarkDialogVisible" width="500px" :before-close="closeRemark">
      <el-form ref="ruleFormRemark" :model="ruleFormRemark" :rules="rulesRemark" label-width="100px">
        <el-form-item label="订单编号" prop="orderNo">
          <el-input v-model="ruleFormRemark.orderNo" disabled />
        </el-form-item>
        <el-form-item label="客户手机" prop="mobile">
          <el-input v-model="ruleFormRemark.mobile" disabled />
        </el-form-item>
        <el-form-item label="客户姓名" prop="name">
          <el-input v-model="ruleFormRemark.name" disabled />
        </el-form-item>
        <el-form-item label="资金渠道" prop="bankChannel">
          <el-input v-model="ruleFormRemark.bankChannel" disabled />
        </el-form-item>
        <el-form-item label="备注类型" prop="remarkType">
          <el-select v-model="ruleFormRemark.remarkType" placeholder="请选择" style="width:100%">
            <el-option label="订单备注" value="订单备注" />
            <el-option label="工单备注" value="工单备注" />
            <el-option label="催收备注" value="催收备注" />
            <el-option label="投诉备注" value="投诉备注" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注定义" prop="remarkDefine">
          <el-select v-model="ruleFormRemark.remarkDefine" placeholder="请选择" style="width:100%">
            <el-option label="贷款业务咨询" value="贷款业务咨询" />
            <el-option label="审批放款查询" value="审批放款查询" />
            <el-option label="异常反馈" value="异常反馈" />
            <el-option label="沟通还款" value="沟通还款" />
            <el-option label="投诉争议" value="投诉争议" />
            <el-option label="无效通话" value="无效通话" />
            <el-option label="帮助建议" value="帮助建议" />
          </el-select>
        </el-form-item>
        <el-form-item label="订单备注" prop="remark">
          <el-input v-model="ruleFormRemark.remark" type="textarea" :rows="4" />
        </el-form-item>
        <el-form-item>
          <el-button round @click="closeRemark">
            取消
          </el-button>
          <el-button round type="primary" @click="submitFormRemark">
            确认
          </el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 电话外呼  -->

    <el-dialog class="call-dialog-class" title="电话外呼" :visible.sync="phoneDialogVisible"
      top="0"
      append-to-body
      :lock-scroll="false"
      :close-on-click-modal="false"
      :show-close="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      :modal="false"
      :modal-append-to-body="false"
      :width="phoneModalWidth"
      custom-class="draggable-dialog"
      @open="handleDialogOpen">
      <!-- 呼叫框 -->
      <div class="phoneCallModal">
        <el-row v-if="showPhonecall" class="phoneBox" id="new-tel-modal-content">
          <img style="width: 50px;margin-bottom: 20px;" src="@/assets/images/phone-call.png" draggable="false" alt="" />
          <div id="____ccbar_ser_status____">{{ callStatusText }}</div>
          <div id="____ccbar_sig_status____"></div>


          <!-- 呼叫状态 -->
          <p class="phone-number">{{ currentRow.mobile || '' }}</p>

          <!-- 按钮区域 -->
          <div class="buttons">
            <el-button id="____ccbar_hangup____" class="btnBox" :disabled="hangupDisabled">
              <img src="@/assets/images/hangup.png" draggable="false" alt="" />
              <div>挂断</div>
            </el-button>
            <el-button id="____ccbar_cahold____" class="btnBox" :disabled="caholdDisabled">
              <img src="@/assets/images/unhold.png" draggable="false" alt="" />
              <div>保持</div>
            </el-button>
            <el-button id="____ccbar_unhold____" class="btnBox" :disabled="unholdDisabled">
              <img src="@/assets/images/resume-call.png" draggable="false" alt="" />
              <div>恢复</div>
            </el-button>
          </div>
          <input :value="currentRow.mobile || ''" id="____ccbar_numb_input____" style="visibility: hidden;" />
          <button ref="ccbarDailto" id="____ccbar_dailto____" style="visibility: hidden;"/>
          <button id='____ccbar_signou____' style="visibility: hidden;"/>
          <audio id='remoteAudio' autoPlay></audio>
        </el-row>
        <!-- 备注框 -->
        <el-row v-if="showRemark" class="remarkBox">
          <el-form ref="ruleFormCall" :model="ruleFormCall" inline size="mini" :rules="rulesCall" label-width="100px">
            <el-form-item label="接听情况" prop="callState">
              <el-select v-model="ruleFormCall.callState" placeholder="请选择" style="width:100%">
                <el-option v-for="item in dict.callState" :label="item.label" :value="item.value" :key="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label-width="40px" label=" ">
              <el-input v-model="ruleFormCall.description" maxlength="100" show-word-limit placeholder="此处填写通话备注" type="textarea" :rows="6" style="width: 260px;" />
            </el-form-item>
            <el-form-item label-width="40px" label=" ">
              <el-button type="primary" @click="submitFormCall">保 存</el-button>
              <el-button type="primary" plain @click="closePhone('remark')">关 闭</el-button>
            </el-form-item>
          </el-form>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { complaintNote, addComplaint, updateComplaint, addOrderRemark } from '@/api/customer'
import { handleCall, callSave, queryCallRecord } from '@/api/order'
import { hex_sha512 } from '@/utils/sha512.js'
export default {
  name: 'ComplaintRemarks',
  props: {
    currentRow: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    titleVal() {
      return this.mode === 'add' ? '新增投诉' : '编辑投诉'
    },
    phoneModalWidth() {
      let width = 0
      if (this.showPhonecall) {
        width+= 240
        if (!this.showRemark) width+= 28
      }
      if (this.showRemark) width+= 360
      if (width === 0) width = 360
      return width + 'px'
    }
  },
  dicts: ["callState"],
  data() {
    return {
      faceDialogVisible: false,
      phoneDialogVisible: false, //  // 是否显示电话弹窗
      showPhonecall: false, //  是否显示电话外呼
      showRemark: false, //  是否显示备注
      callStatusText: '空闲',
      hangupDisabled: true, // 挂断按钮是否禁用
      unholdDisabled: true, // 恢复按钮是否禁用
      caholdDisabled: true, // 保持按钮是否禁用
      isDragging: false, // 是否正在拖拽
      startX: 0, // 拖拽起始 X 坐标
      startY: 0, // 拖拽起始 Y 坐标
      dialogOffsetX: 0, // 弹窗偏移 X
      dialogOffsetY: 0, // 弹窗偏移 Y

      loading: false,
      list: [],
      total: 0,
      // 投诉、备注
      customerComplainList: [],
      orderRemarkList: [],
      customerCallList: [], // 通话备注记录

      // 投诉弹窗
      mode: '',
      complaintDialogVisible: false,
      ruleForm: {},
      rules: {
        name: [{ required: true, message: '姓名不能为空', trigger: 'blur' }],
        mobile: [{ required: true, message: '手机号不能为空', trigger: 'change' }],
        orderNo: [{ required: true, message: '订单号不能为空', trigger: 'change' }],
        bankChannel: [{ required: true, message: '资方渠道不能为空', trigger: 'change' }],
        complainChannel: [{ required: true, message: '投诉渠道不能为空', trigger: 'change' }],
        mainItem: [{ required: true, message: '主要事项不能为空', trigger: 'change' }],
        applyTime: [{ required: true, message: '申请时间不能为空', trigger: 'change' }],
        loanAmount: [{ required: true, message: '贷款金额不能为空', trigger: 'change' }],
        gender: [{ required: true, message: '性别不能为空', trigger: 'change' }]
      },
      submiting: false,

      // 备注弹窗
      remarkDialogVisible: false,
      ruleFormRemark: {},
      rulesRemark: {
        name: [{ required: true, message: '姓名不能为空', trigger: 'blur' }],
        mobile: [{ required: true, message: '手机号不能为空', trigger: 'change' }],
        orderNo: [{ required: true, message: '订单号不能为空', trigger: 'change' }],
        remark: [{ required: true, message: '订单备注不能为空', trigger: 'change' }]
      },
      ruleFormCall: {},
      rulesCall: {
        callState: [{ required: true, message: '接听情况不能为空', trigger: 'change' }]
      },
      phoneRef: ''
    }
  },
  activated() {
    // this.phoneRef = ''
  },
  created() {
  },

  methods: {
    // 打开外呼弹窗
    async openPhone() {
      // 通话中，不允许再次拨打
      if (this.phoneDialogVisible) {
        this.$message.error('正在通话中~')
        return
      }
      // 获取外呼坐席
      const userData = await handleCall()
      if (userData.code != '000000') return
      this.phoneDialogVisible = true
      this.showPhonecall = true
      this.showRemark = true
      // 初始化webPhoneSDK
      this.initWebPhoneSDK(userData.data)
    },
    // 关闭外呼弹窗内的 外呼 或 备注
    closePhone(type) {
      switch (type) {
        case 'phone':
          if (!this.showRemark) this.phoneDialogVisible = false
          this.showPhonecall = false
          break
        case 'remark':
          if (!this.showPhonecall) this.phoneDialogVisible = false
          this.showRemark = false
          break
        default:
          break
      }
      if (!this.phoneDialogVisible) {
        this.hangupDisabled = true // 挂断按钮是否禁用
        this.unholdDisabled = true // 恢复按钮是否禁用
        this.caholdDisabled = true // 保持按钮是否禁用
        this.phoneRef = ''
        this.ruleFormCall = {
          callState: '',
          description: ''
        }
      }
    },
    // 弹窗打开时初始化拖拽事件
    handleDialogOpen() {
      const dialog = document.querySelector('.draggable-dialog');
      const header = dialog.querySelector('.el-dialog__header');

      // 监听鼠标按下事件
      // header.addEventListener('mousedown', (e) => {
      dialog.addEventListener('mousedown', (e) => {
        if (e.target.nodeName === 'INPUT' || e.target.nodeName === 'TEXTAREA') return
        this.isDragging = true;
        this.startX = e.clientX;
        this.startY = e.clientY;
        this.dialogOffsetX = dialog.offsetLeft;
        this.dialogOffsetY = dialog.offsetTop;

        // 监听鼠标移动事件
        document.addEventListener('mousemove', this.handleMouseMove);

        // 监听鼠标松开事件
        document.addEventListener('mouseup', this.handleMouseUp);
      });
    },
    // 处理鼠标移动
    handleMouseMove(e) {
      if (e.target.nodeName === 'INPUT' || e.target.nodeName === 'TEXTAREA') return
      if (this.isDragging) {
        const dialog = document.querySelector('.draggable-dialog');
        const offsetX = e.clientX - this.startX;
        const offsetY = e.clientY - this.startY;
        const clientWidth = document.documentElement.clientWidth;
        const clientHeight = document.documentElement.clientHeight;
        const offsetWidth = dialog.offsetWidth;
        const offsetHeight = dialog.offsetHeight;
        const left = this.dialogOffsetX + offsetX; // 元素左偏移量
        const top = this.dialogOffsetY + offsetY; // 元素上偏移量
        // 更新弹窗位置
        if (left > 0) {
          dialog.style.left = `${left}px`;

          if (left + offsetWidth < clientWidth) {
            dialog.style.left = `${left}px`;
          } else dialog.style.left = `${clientWidth - offsetWidth}px`;
        } else dialog.style.left = 0;

        if (top > 0 ) {
          dialog.style.top = `${top}px`;
          if (top + offsetHeight < clientHeight) {
            dialog.style.top = `${top}px`;
          } else dialog.style.top = `${clientHeight - offsetHeight}px`;
        } else dialog.style.top = 0;
      }
    },
    // 处理鼠标松开
    handleMouseUp() {
      this.isDragging = false;

      // 移除事件监听
      document.removeEventListener('mousemove', this.handleMouseMove);
      document.removeEventListener('mouseup', this.handleMouseUp);
    },
    // 获取订单相关投诉、备注
    getComplaintNote() {
      complaintNote({ orderId: this.currentRow.orderId }).then(res => {
        this.customerComplainList = res.data.customerComplainList || []
        this.orderRemarkList = res.data.orderRemarkLis || []
      })
    },
    // 获取投诉列表
    getCallRecord() {
      const params = {
        orderId: this.currentRow.orderId,
        phone: this.currentRow.mobile,
        pageNum: 1,
        pageSize: 1000
      }
      queryCallRecord(params).then(res => {
        this.customerCallList = res.data.list || []
      })
    },

    // 新增投诉
    handleAddComplaint() {
      if (!this.currentRow) {
        this.$message.warning('请先选择1个订单')
        return
      }
      this.mode = 'add'
      this.ruleForm = {
        orderNo: this.currentRow.orderId, // 订单编号
        name: this.currentRow.name, // 用户姓名
        mobile: this.currentRow.mobile, // 手机号
        bankChannel: this.currentRow.bankChannel, //	资金方
        gender: this.currentRow.gender, // 性别
        loanAmount: this.currentRow.applyAmount, // 贷款金额
        complainChannel: undefined, //	投诉渠道
        complainType: undefined, //	投诉类型
        mainItem: undefined, //	主要事项
        remark: undefined, //	投诉备注
        channelUrl: undefined, //	渠道链接
        overdueState: undefined, // 是否逾期
        applyTime: this.currentRow.applyTime, // 申请时间
        subscribeResolveTime: undefined, //	预约处理时间
        discontinuationState: undefined //	是否停催
      }
      this.complaintDialogVisible = true
    },

    // 编辑投诉
    handleEditeComplaint(row) {
      console.log(row)

      this.mode = 'edite'
      this.ruleForm = {
        id: row.id, //	投诉id
        mobile: row.mobile, //	手机号
        complainChannel: row.complainChannel, //	投诉渠道
        channelUrl: row.channelUrl, //	渠道链接
        complainType: row.complainType ? row.complainType.split(',') : [], //	投诉类型
        mainItem: row.mainItem, //	主要事项
        remark: row.remark, //	投诉备注
        subscribeResolveTime: row.subscribeResolveTime, //	预约处理时间
        discontinuationState: row.discontinuationState, //	是否停催
        assistPerson: row.assistPerson, // 协助人员
        completeTime: row.completeTime, // 完成时间
        handleState: row.handleState, // 处理状态
        reduceAmt: row.reduceAmt // 关怀金/减免金额
      }
      this.complaintDialogVisible = true
    },

    // 提交投诉
    submitForm() {
      this.$refs['ruleForm'].validate(valid => {
        if (valid) {
          const params = {
            ...this.ruleForm,
            complainType: this.ruleForm.complainType ? this.ruleForm.complainType.join(',') : undefined
          }

          this.submiting = true

          if (this.mode === 'add') {
            addComplaint(params).then(res => {
              this.$message.success('新增投诉成功！')
              this.closeComplaint()
              this.getComplaintNote()
            }).finally(() => {
              this.submiting = false
            })
          } else if (this.mode === 'edite') {
            updateComplaint(params).then(res => {
              this.$message.success('编辑投诉成功！')
              this.closeComplaint()
              this.getComplaintNote()
            }).finally(() => {
              this.submiting = false
            })
          }
        }
      })
    },

    // 关闭投诉弹窗
    closeComplaint() {
      this.complaintDialogVisible = false
      this.ruleForm = {}
      this.$refs['ruleForm'].resetFields()
    },

    // 新增备注
    handleAddRemark() {
      if (!this.currentRow) {
        this.$message.warning('请先选择1个订单')
        return
      }
      this.ruleFormRemark = {
        orderNo: this.currentRow.orderId, // 订单编号
        name: this.currentRow.name, // 用户姓名
        mobile: this.currentRow.mobile, // 手机号
        bankChannel: this.currentRow.bankChannel, //	资金渠道
        remarkType: undefined, // 备注类型
        remarkDefine: undefined, //	备注定义
        remark: undefined //	订单备注
      }
      this.remarkDialogVisible = true
    },

    // 提交备注
    submitFormRemark() {
      this.$refs['ruleFormRemark'].validate(valid => {
        if (valid) {
          addOrderRemark(this.ruleFormRemark).then(res => {
            this.$message.success('新增备注成功！')
            this.closeRemark()
            this.getComplaintNote()
          })
        }
      })
    },
    // 提交外呼备注
    submitFormCall() {
      this.$refs['ruleFormCall'].validate(valid => {
        if (valid) {
          const params = {
            orderId: this.currentRow.orderId, // 订单编号
            phone: this.currentRow.mobile, // 手机号
            ...this.ruleFormCall
          }
          callSave(params).then(res => {
            this.$message.success('新增备注成功！')
            this.closePhone('remark')
            this.getCallRecord()
          })
        }
      })
    },

    // 关闭备注弹窗
    closeRemark() {
      this.remarkDialogVisible = false
      this.ruleFormRemark = {}
      this.$refs['ruleFormRemark'].resetFields()
    },

    // 初始化坐席SDK
    initWebPhoneSDK(userData = {}) {
      const that = this
      this.phoneRef = new CCBarSDK({
        useExtraErrorHandle: true,
        eventHandle: {
          onActionEnd(type, isSuccess) {
            console.log("onActionEnd", type, isSuccess);
            // 可以根据需要添加操作成功或失败的逻辑

            if (type === "hangup" && isSuccess) {
              that.sendMessage({ errorCode: "通话结束" });
            }
          },
          onRequestError(type, data) {
            console.log("~ ~ ~onRequestError事件：", type, data);
            that.sendMessage({
              errorCode: data && data.message ? data.message : "请求失败",
            });
          },
          onError(msg) {
            console.log("🚀 ~—— onError ~ msg:", msg);
            if (msg !== "登录成功!") {
              that.sendMessage({ errorCode: msg });
            }
          },
          onRecviceCall(number, data) {
            console.log(`接收到呼叫，号码：${number}`, "数据：", data);
          },
          onStatusChange(workStatus, serviceStatus) {
            console.log(`工作状态：${workStatus}`, `服务状态：${serviceStatus}`);

            if (Number(serviceStatus) === 1) {
              // $("#____ccbar_hangup____").prop("disabled", false);
              that.hangupDisabled = false; // 接通后可以挂断
            }
          },
          onWebPhoneHandle(type, data, options) {
            console.log("webPhone事件：", type, data, options);
            that.handleWebPhoneEvent(type, data, options);
          },
          onMessage(data) {
            console.log("🚀 ~ onMessage事件 ~ data:", data);
            if (data && Number(data.type) === 4) {
              that.sendMessage({ errorCode: data.body.err_msg });
            }
          },
        },
      });
      // 测试数据，正式环境需要替换成真实的客户号、用户名和密码
      // const customer = "催收测试";
      // const username = "1020";
      // const password = "R7f$K9!q2pL1@jM3nZ5x"
      let config = {
        url: "https://ocs.qiangyun.com",
        // url: "http://106.14.149.48",
        type: "userinfo",
        lineMode: "webPhone",
        ...userData,
        password: hex_sha512(userData.password)
      };

      if (this.phoneRef) {
        this.phoneRef.login(config);
        setTimeout(() => {
          this.$nextTick(() => {
            this.$refs.ccbarDailto.click();
          })
          // $("#____ccbar_dailto____").click();
        }, 1000);
      }
    },
    // 处理WebPhone相关事件
    handleWebPhoneEvent(type, data, options) {
      switch (type) {
        case "ua.connecting":
          console.log("WEB电话正在连接...");
          break;
        case "ua.connected":
          console.log("WEB电话连接成功");
          break;
        case "ua.disconnected":
          this.phoneRef && this.phoneRef.webPhone.stop();
          this.hangupDisabled = true; // 挂断后按钮禁用
          this.sendMessage({ errorCode: "WEB电话连接已断开" });
          break;
        case "reg.registered":
          console.log("WEB电话分机已注册");
          break;
        case "reg.unregistered":
          this.sendMessage({ errorCode: "WEB电话分机已取消注册" });
          break;
        case "reg.failed":
          this.sendMessage({ errorCode: "WEB电话分机注册失败" });
          break;
        case "incoming.notify":
          console.log("新的呼出，号码为", data.name);
          this.hangupDisabled = false; // 接通后可以挂断
          this.caholdDisabled = false; // 接通后可以取消保持
          // $("#____ccbar_hangup____").prop("disabled", false);
          // $("#____ccbar_cahold____").prop("disabled", false);

          this.phoneRef.webPhone.answer();
          break;
        case "incoming.accepted":
          console.log("incoming.accepted, 已接听");
          break;
        case "incoming.failed":
          console.log("呼出失败", data)
          this.sendMessage({ errorCode: data.cause || data.message || "呼出失败" });
          break;
        case "incoming.ended":
          this.phoneRef.webPhone && this.phoneRef.webPhone.stop();
          this.sendMessage({ errorCode: "呼出结束～" });
          this.$message.warning("呼出结束～")
          break;
        case "call.hold":
          this.caholdDisabled = true; // 不可以保持
          this.unholdDisabled = false; // 可以恢复
          // $("#____ccbar_cahold____").prop("disabled", true);
          // $("#____ccbar_unhold____").prop("disabled", false);
          break;
        case "call.unhold":
          this.unholdDisabled = true; // 不可以恢复
          this.caholdDisabled = false; // 可以保持
          // $("#____ccbar_unhold____").prop("disabled", true);
          // $("#____ccbar_cahold____").prop("disabled", false);
          break;
        default:
          break;
      }
    },
    sendMessage(data) {
      console.log("🚀 ~ this.sendMessage ~ data:", data);
      this.closePhone('phone')
      // this.$message.warning(data.errorCode || "未知错误")
    }
  }
}
</script>
<style>
  .call-dialog-class .el-dialog {
    pointer-events: auto;
  }

  body>.call-dialog-class {
    pointer-events: none;
    user-select: none; /* 禁止文本选中 */
    -webkit-user-select: none; /* 兼容 Safari 和 Chrome */
    -moz-user-select: none; /* 兼容 Firefox */
    -ms-user-select: none; /* 兼容 IE 和 Edge */
  }
  /* 弹窗样式 */
  .call-dialog-class .draggable-dialog {
    position: absolute;
    top: 15vh;
    left: calc(50% - 300px);
    margin: 0;
  }

  /* 弹窗头部样式 */
  .call-dialog-class.draggable-dialog .el-dialog__header {
    cursor: pointer;
  }
</style>
<style lang="scss" scoped>
.complaint-remark {
  .title-box span {
    font-size: 14px;
    font-weight: 500;
    display: inline-block;
    width: 200px;
  }

  .text-box {
    padding: 10px;
    border: #e5e5e5 1px solid;
    background: #f5f5f5;
    line-height: 1.4;
  }
}

.phoneCallModal {
  width: 100%;
  display: flex;
  justify-content: center;
  .phoneBox {
    height: 248px;
    flex: 2;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    .buttons {
      display: flex;
      justify-content: space-around;
      align-items: center;
      cursor: pointer;
      .btnBox {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        margin: 26px 3px 0;
        text-align: center;
        background: #eeeeee;
        padding-top: 10px;
        font-size: 12px;
        img {
          width: 30px;
          height: 30px;
          margin-bottom: 6px;
        }
      }
    }
  }
  .remarkBox {
    flex: 3;
  }
}
</style>
