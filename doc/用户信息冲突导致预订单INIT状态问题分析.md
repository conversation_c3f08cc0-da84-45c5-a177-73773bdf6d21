# 用户信息冲突导致预订单INIT状态问题分析

## 📋 问题详情

**订单信息**:
- 订单号: POR25080220032026823700734336379
- 第一次申请: 8.2 6点 (有风控记录)
- 第二次申请: 8.2 20点 (相同手机号，不同姓名和身份证号)
- 问题: 第二次申请的预订单risk_id字段为空，卡在INIT状态

## 🔍 根本原因分析

### 问题核心：用户信息冲突处理异常

根据代码分析，问题出现在 `ApprovalOperateService.approval()` 方法的用户注册环节：

<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/entrance/common/service/ApprovalOperateService.java" mode="EXCERPT">
```java
//注册用户
UserRegister userRegister = userService.findUserRegisterByOpenId(preOrder.getOpenId(), preOrder.getFlowChannel());
if (Objects.isNull(userRegister)) {
    userRegister = userService.registerRecord(preOrder.getMobile(), preOrder.getOpenId(), preOrder.getFlowChannel());
}
UserInfo userInfo = createUserInfo(record);
UserOcr userOcr = createUserOcr(record);
UserFace userFace = createUserFace(record);
UserDevice userDevice = createUserDevice(record);
List<UserContactInfo> contactInfos = createUserContactInfos(record);
UserRiskRecord riskRecord = userService.register(userInfo, userOcr, userFace, userDevice, contactInfos, ThreadLocalUtil.getFlowChannel());
//保存风控id
preOrder.setRiskId(riskRecord.getId());
```
</augment_code_snippet>

### 具体分析：

#### 1. 用户注册逻辑
<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/service/UserService.java" mode="EXCERPT">
```java
public UserRiskRecord register(UserInfo userInfo, UserOcr ocr, UserFace face, UserDevice device, List<UserContactInfo> contactInfos, FlowChannel flowChannel, String applyChannel) {
    
    UserInfo existUserInfo = userInfoRepository.findByCertNo(userInfo.getCertNo());
    if (existUserInfo == null) {
        // 创建新用户
        existUserInfo = createUser(userInfo, ocr, face, device, contactInfos);
        saveUserInfoExpand(existUserInfo);
    } else {
        // 更新现有用户
        existUserInfo = updateUser(existUserInfo, userInfo, ocr, face, device, contactInfos);
    }
    return createRiskRecord(existUserInfo.getId(), flowChannel, applyChannel, userInfo.getCertNo());
}
```
</augment_code_snippet>

#### 2. 问题场景重现：

**第一次申请 (8.2 6点)**:
- 手机号: 138****1234
- 姓名: 张三
- 身份证: 110101199001011234
- 结果: 成功创建用户和风控记录

**第二次申请 (8.2 20点)**:
- 手机号: 138****1234 (相同)
- 姓名: 李四 (不同)
- 身份证: 220101199001015678 (不同)
- 问题: 系统按身份证号查找用户，发现不存在，尝试创建新用户

#### 3. 可能的异常情况：

**情况A: 手机号唯一性约束冲突**
```java
// 在createUser过程中，可能因为手机号唯一性约束导致创建失败
// 但异常被捕获或处理不当，导致riskRecord创建失败
```

**情况B: 数据库事务回滚**
```java
// 在用户创建过程中发生异常，事务回滚
// 但preOrder已经保存，risk_id未能正确设置
```

**情况C: 并发问题**
```java
// 两个相同手机号的请求并发处理
// 导致数据不一致
```

## 🔧 详细排查步骤

### 1. 数据库查询脚本
```sql
-- 查询该手机号的所有用户信息
SELECT 
    ui.id, ui.name, ui.cert_no, ui.mobile, ui.created_time,
    ur.id as register_id, ur.open_id, ur.source_channel
FROM user_info ui
LEFT JOIN user_register ur ON ui.id = ur.user_id
WHERE ui.mobile = '138****1234'  -- 替换为实际手机号
ORDER BY ui.created_time;

-- 查询该手机号的所有风控记录
SELECT 
    urr.id, urr.user_id, urr.approve_result, urr.flow_channel,
    urr.created_time, urr.updated_time,
    ui.name, ui.cert_no, ui.mobile
FROM user_risk_record urr
LEFT JOIN user_info ui ON urr.user_id = ui.id
WHERE ui.mobile = '138****1234'  -- 替换为实际手机号
ORDER BY urr.created_time;

-- 查询该手机号的所有预订单
SELECT 
    po.id, po.order_no, po.pre_order_state, po.risk_id,
    po.name, po.cert_no, po.mobile, po.created_time, po.remark
FROM pre_order po
WHERE po.mobile = '138****1234'  -- 替换为实际手机号
ORDER BY po.created_time;

-- 查询用户注册记录
SELECT 
    ur.id, ur.user_id, ur.open_id, ur.mobile, ur.source_channel,
    ur.register_state, ur.created_time
FROM user_register ur
WHERE ur.mobile = '138****1234'  -- 替换为实际手机号
ORDER BY ur.created_time;
```

### 2. 日志关键词搜索
在应用日志中搜索：
- `POR25080220032026823700734336379`
- `用户注册异常`
- `createUser`
- `register`
- `手机号重复`
- `身份证号冲突`
- `数据库约束异常`

## 🚨 可能的异常类型

### 1. 数据库约束异常
```java
// 手机号唯一性约束
Duplicate entry '138****1234' for key 'uk_mobile'

// 身份证号唯一性约束  
Duplicate entry '220101199001015678' for key 'uk_cert_no'
```

### 2. 业务逻辑异常
```java
// 用户ID已存在异常
throw new LvxinBizException(LvxinResultCode.USER_ID_AREADY_EXISTS);
```

### 3. 事务异常
```java
// 事务回滚导致数据不一致
TransactionSystemException
```

## 💡 修复方案

### 方案1: 数据修复
```sql
-- 1. 找到对应的风控记录ID
SELECT urr.id as risk_id
FROM user_risk_record urr
JOIN user_info ui ON urr.user_id = ui.id
WHERE ui.mobile = '138****1234'  -- 实际手机号
  AND urr.created_time >= '2025-08-02 20:00:00'
  AND urr.created_time <= '2025-08-02 21:00:00'
ORDER BY urr.created_time DESC
LIMIT 1;

-- 2. 更新预订单的risk_id
UPDATE pre_order 
SET risk_id = '上面查询到的风控记录ID',
    remark = CONCAT(IFNULL(remark, ''), ' [手动修复用户冲突-', NOW(), ']'),
    updated_time = NOW()
WHERE order_no = 'POR25080220032026823700734336379';

-- 3. 更新预订单状态为AUDITING并重新提交风控
UPDATE pre_order 
SET pre_order_state = 'AUDITING',
    updated_time = NOW()
WHERE order_no = 'POR25080220032026823700734336379';
```

### 方案2: 程序修复
```java
@RestController
@RequestMapping("/admin/fix")
public class UserConflictFixController {
    
    /**
     * 修复用户信息冲突导致的预订单问题
     */
    @PostMapping("/preorder/user-conflict/{orderNo}")
    public ResponseEntity<?> fixUserConflictPreOrder(@PathVariable String orderNo) {
        try {
            PreOrder preOrder = preOrderRepository.findByOrderNo(orderNo)
                .orElseThrow(() -> new RuntimeException("预订单不存在"));
            
            if (StringUtil.isNotBlank(preOrder.getRiskId())) {
                return ResponseEntity.badRequest().body("预订单已有风控记录ID");
            }
            
            // 根据手机号查找最近的风控记录
            List<UserRiskRecord> riskRecords = findRiskRecordsByMobile(preOrder.getMobile());
            
            if (riskRecords.isEmpty()) {
                return ResponseEntity.badRequest().body("未找到对应的风控记录");
            }
            
            // 选择最合适的风控记录
            UserRiskRecord targetRiskRecord = selectBestMatchRiskRecord(preOrder, riskRecords);
            
            // 更新预订单
            preOrder.setRiskId(targetRiskRecord.getId());
            preOrder.setRemark("手动修复用户冲突-" + LocalDateTime.now());
            preOrderRepository.save(preOrder);
            
            // 重新提交风控
            mqService.submitRiskApply(targetRiskRecord.getId());
            
            // 更新状态
            preOrder.setPreOrderState(PreOrderState.AUDITING);
            preOrderRepository.save(preOrder);
            
            return ResponseEntity.ok("修复成功");
            
        } catch (Exception e) {
            logger.error("修复用户冲突预订单失败", e);
            return ResponseEntity.status(500).body("修复失败: " + e.getMessage());
        }
    }
}
```

## 🔄 预防措施

### 1. 增强用户信息验证
```java
// 在用户注册前增加更严格的验证
public void validateUserInfo(String mobile, String certNo, String name) {
    // 检查手机号是否已被其他身份证号使用
    UserInfo existByMobile = userInfoRepository.findByMobile(mobile);
    if (existByMobile != null && !existByMobile.getCertNo().equals(certNo)) {
        throw new BizException("手机号已被其他用户使用");
    }
    
    // 检查身份证号是否已被其他手机号使用
    UserInfo existByCertNo = userInfoRepository.findByCertNo(certNo);
    if (existByCertNo != null && !existByCertNo.getMobile().equals(mobile)) {
        throw new BizException("身份证号已被其他用户使用");
    }
}
```

### 2. 完善异常处理
```java
try {
    UserRiskRecord riskRecord = userService.register(userInfo, userOcr, userFace, userDevice, contactInfos, ThreadLocalUtil.getFlowChannel());
    preOrder.setRiskId(riskRecord.getId());
    preOrder = preOrderRepository.save(preOrder);
} catch (Exception e) {
    logger.error("用户注册失败，预订单：{}", preOrder.getOrderNo(), e);
    preOrder.setPreOrderState(PreOrderState.AUDIT_REJECT);
    preOrder.setRemark("用户注册失败：" + e.getMessage());
    preOrderRepository.save(preOrder);
    throw new CommonApiBizException(CommonApiResultCode.USER_REGISTER_FAILED);
}
```

### 3. 添加监控告警
```java
// 监控risk_id为空的预订单
@Scheduled(fixedRate = 300000) // 5分钟检查一次
public void monitorEmptyRiskIdPreOrders() {
    List<PreOrder> emptyRiskIdOrders = preOrderRepository.findByRiskIdIsNullAndCreatedTimeAfter(
        LocalDateTime.now().minusHours(1));
    
    if (!emptyRiskIdOrders.isEmpty()) {
        warningService.warn("发现" + emptyRiskIdOrders.size() + "个risk_id为空的预订单");
    }
}
```

这个问题的根本原因是**相同手机号但不同身份信息的用户在注册过程中发生冲突**，导致风控记录创建失败，但预订单已经保存，从而出现risk_id为空的情况。
