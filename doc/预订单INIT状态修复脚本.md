# 预订单INIT状态修复脚本

## 📋 问题订单信息
- **订单号**: POR25080220032026823700734336379
- **当前状态**: INIT
- **问题**: 预订单未能正常流转到AUDITING状态

## 🔧 快速修复方案

### 方案1: 手动触发风控处理

#### 1.1 查询订单详细信息
```java
@RestController
@RequestMapping("/admin/fix")
public class PreOrderFixController {
    
    @Autowired
    private PreOrderRepository preOrderRepository;
    
    @Autowired
    private UserRiskRecordRepository userRiskRecordRepository;
    
    @Autowired
    private MqService mqService;
    
    /**
     * 查询问题订单详情
     */
    @GetMapping("/preorder/detail/{orderNo}")
    public ResponseEntity<?> getPreOrderDetail(@PathVariable String orderNo) {
        Optional<PreOrder> preOrderOpt = preOrderRepository.findByOrderNo(orderNo);
        if (preOrderOpt.isEmpty()) {
            return ResponseEntity.notFound().build();
        }
        
        PreOrder preOrder = preOrderOpt.get();
        Map<String, Object> result = new HashMap<>();
        result.put("preOrder", preOrder);
        
        // 查询风控记录
        if (StringUtil.isNotBlank(preOrder.getRiskId())) {
            Optional<UserRiskRecord> riskRecord = userRiskRecordRepository.findById(preOrder.getRiskId());
            result.put("riskRecord", riskRecord.orElse(null));
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 手动修复INIT状态的预订单
     */
    @PostMapping("/preorder/fix/{orderNo}")
    public ResponseEntity<?> fixPreOrder(@PathVariable String orderNo) {
        try {
            Optional<PreOrder> preOrderOpt = preOrderRepository.findByOrderNo(orderNo);
            if (preOrderOpt.isEmpty()) {
                return ResponseEntity.badRequest().body("订单不存在");
            }
            
            PreOrder preOrder = preOrderOpt.get();
            
            // 检查当前状态
            if (preOrder.getPreOrderState() != PreOrderState.INIT) {
                return ResponseEntity.badRequest().body("订单状态不是INIT，当前状态：" + preOrder.getPreOrderState());
            }
            
            // 检查是否有风控记录
            if (StringUtil.isBlank(preOrder.getRiskId())) {
                return ResponseEntity.badRequest().body("风控记录ID为空，需要先创建风控记录");
            }
            
            // 查询风控记录
            Optional<UserRiskRecord> riskRecordOpt = userRiskRecordRepository.findById(preOrder.getRiskId());
            if (riskRecordOpt.isEmpty()) {
                return ResponseEntity.badRequest().body("风控记录不存在");
            }
            
            UserRiskRecord riskRecord = riskRecordOpt.get();
            
            // 检查风控记录状态
            if (riskRecord.getApproveResult() != AuditState.INIT) {
                return ResponseEntity.badRequest().body("风控记录状态不是INIT，当前状态：" + riskRecord.getApproveResult());
            }
            
            // 重新提交风控申请
            mqService.submitRiskApply(riskRecord.getId());
            
            // 更新预订单状态
            preOrder.setPreOrderState(PreOrderState.AUDITING);
            preOrder.setRemark("手动修复-重新提交风控");
            preOrderRepository.save(preOrder);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "修复成功，已重新提交风控申请");
            result.put("riskId", riskRecord.getId());
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            logger.error("修复预订单异常，订单号：{}", orderNo, e);
            return ResponseEntity.status(500).body("修复失败：" + e.getMessage());
        }
    }
}
```

#### 1.2 批量修复脚本
```java
/**
 * 批量修复INIT状态的预订单
 */
@Service
public class PreOrderFixService {
    
    @Autowired
    private PreOrderRepository preOrderRepository;
    
    @Autowired
    private UserRiskRecordRepository userRiskRecordRepository;
    
    @Autowired
    private MqService mqService;
    
    private static final Logger logger = LoggerFactory.getLogger(PreOrderFixService.class);
    
    /**
     * 批量修复超时的INIT状态预订单
     */
    @Transactional
    public void batchFixTimeoutInitPreOrders() {
        // 查询超过30分钟仍为INIT状态的预订单
        LocalDateTime timeoutThreshold = LocalDateTime.now().minusMinutes(30);
        
        List<PreOrder> timeoutPreOrders = preOrderRepository.findByPreOrderStateAndCreatedTimeBefore(
            PreOrderState.INIT, timeoutThreshold);
        
        logger.info("发现{}个超时的INIT状态预订单", timeoutPreOrders.size());
        
        for (PreOrder preOrder : timeoutPreOrders) {
            try {
                fixSinglePreOrder(preOrder);
            } catch (Exception e) {
                logger.error("修复预订单失败，订单号：{}", preOrder.getOrderNo(), e);
            }
        }
    }
    
    private void fixSinglePreOrder(PreOrder preOrder) {
        logger.info("开始修复预订单：{}", preOrder.getOrderNo());
        
        // 检查风控记录
        if (StringUtil.isBlank(preOrder.getRiskId())) {
            logger.warn("预订单{}风控记录ID为空，跳过修复", preOrder.getOrderNo());
            return;
        }
        
        Optional<UserRiskRecord> riskRecordOpt = userRiskRecordRepository.findById(preOrder.getRiskId());
        if (riskRecordOpt.isEmpty()) {
            logger.warn("预订单{}对应的风控记录不存在，跳过修复", preOrder.getOrderNo());
            return;
        }
        
        UserRiskRecord riskRecord = riskRecordOpt.get();
        
        // 只处理INIT状态的风控记录
        if (riskRecord.getApproveResult() != AuditState.INIT) {
            logger.info("预订单{}对应的风控记录状态为{}，跳过修复", 
                preOrder.getOrderNo(), riskRecord.getApproveResult());
            return;
        }
        
        // 重新提交风控申请
        mqService.submitRiskApply(riskRecord.getId());
        
        // 更新预订单状态
        preOrder.setPreOrderState(PreOrderState.AUDITING);
        preOrder.setRemark("自动修复-重新提交风控-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        preOrderRepository.save(preOrder);
        
        logger.info("预订单{}修复成功，风控ID：{}", preOrder.getOrderNo(), riskRecord.getId());
    }
}
```

### 方案2: 定时任务监控修复

```java
/**
 * 预订单状态监控定时任务
 */
@Component
public class PreOrderMonitorTask {
    
    @Autowired
    private PreOrderFixService preOrderFixService;
    
    /**
     * 每10分钟检查一次超时的INIT状态预订单
     */
    @Scheduled(fixedRate = 600000) // 10分钟
    public void monitorTimeoutInitPreOrders() {
        try {
            preOrderFixService.batchFixTimeoutInitPreOrders();
        } catch (Exception e) {
            logger.error("预订单监控任务执行异常", e);
        }
    }
}
```

### 方案3: 数据库直接修复（紧急情况）

```sql
-- 仅在紧急情况下使用，需要确保对应的风控记录存在且状态正确

-- 1. 查询需要修复的预订单
SELECT 
    po.id, po.order_no, po.pre_order_state, po.risk_id,
    urr.id as risk_record_id, urr.approve_result
FROM pre_order po
LEFT JOIN user_risk_record urr ON po.risk_id = urr.id
WHERE po.order_no = 'POR25080220032026823700734336379'
  AND po.pre_order_state = 'INIT';

-- 2. 如果风控记录存在且状态为INIT，可以直接更新预订单状态
-- 注意：这种方式需要确保后续的MQ消息能够正常处理
UPDATE pre_order 
SET pre_order_state = 'AUDITING',
    remark = CONCAT(IFNULL(remark, ''), ' [手动修复-', NOW(), ']'),
    updated_time = NOW()
WHERE order_no = 'POR25080220032026823700734336379'
  AND pre_order_state = 'INIT';

-- 3. 同时需要手动触发MQ消息（通过应用程序）
-- mqService.submitRiskApply(riskId);
```

## ⚠️ 注意事项

1. **数据一致性**: 修复前务必检查相关数据的完整性
2. **幂等性**: 确保修复操作可以重复执行而不产生副作用
3. **日志记录**: 所有修复操作都要记录详细日志
4. **监控告警**: 修复后要持续监控订单状态变化
5. **根因分析**: 修复完成后要分析根本原因，防止再次发生

## 🔍 修复后验证

修复完成后，需要验证以下内容：

1. **预订单状态**: 确认状态已更新为AUDITING
2. **风控处理**: 确认风控申请已正常处理
3. **MQ消息**: 确认相关MQ消息已正常消费
4. **后续流程**: 跟踪订单后续状态变化

## 📊 预防措施

1. **增强监控**: 添加预订单状态异常告警
2. **完善日志**: 记录关键步骤的处理结果
3. **异常处理**: 优化异常处理逻辑
4. **定时修复**: 部署自动修复定时任务
